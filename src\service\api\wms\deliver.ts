import { request } from '@/service/request';

export const deliverApi = {
  // 获取出库单列表
  list: (params: Api.Wms.DeliverSearchParams) =>
    request<Api.Wms.DeliverList>({
      url: '/wms/delivers',
      params
    }),

  // 创建出库单
  add: (data: Api.Wms.DeliverCreateParams) =>
    request<null>({
      url: '/wms/delivers',
      method: 'POST',
      data
    }),

  // 更新出库单
  save: (data: Api.Wms.DeliverUpdateParams) =>
    request<null>({
      url: `/wms/delivers/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除出库单
  del: (id: number) =>
    request<null>({
      url: `/wms/delivers/${id}`,
      method: 'DELETE'
    }),

  // 批量删除出库单
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/delivers',
      method: 'DELETE',
      data: { ids }
    })
};
