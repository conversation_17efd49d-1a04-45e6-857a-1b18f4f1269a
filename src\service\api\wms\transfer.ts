import { request } from '@/service/request';

export const transferApi = {
  // 获取调拨单列表
  list: (params: Api.Wms.TransferSearchParams) =>
    request<Api.Wms.TransferList>({
      url: '/wms/transfers',
      params
    }),

  // 创建调拨单
  add: (data: Api.Wms.TransferCreateParams) =>
    request<null>({
      url: '/wms/transfers',
      method: 'POST',
      data
    }),

  // 更新调拨单
  save: (data: Api.Wms.TransferUpdateParams) =>
    request<null>({
      url: `/wms/transfers/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除调拨单
  del: (id: number) =>
    request<null>({
      url: `/wms/transfers/${id}`,
      method: 'DELETE'
    }),

  // 批量删除调拨单
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/transfers',
      method: 'DELETE',
      data: { ids }
    })
};
