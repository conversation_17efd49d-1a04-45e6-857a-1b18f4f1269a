<script setup lang="ts">
import { watch, ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { itemApi, skuApi } from '@/service/api/wms';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.Wms.StockSearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}

const skuOptions = ref<Api.Wms.Sku[]>([]);

watch(() => model.value.itemId, async (itemId: number | null | undefined) => {
  if (!itemId) {
    skuOptions.value = [];
    model.value.skuId = null;
    return;
  }

  const { data, error } = await skuApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    itemId,
  });
  if (!error) {
    skuOptions.value = data.records
  }
});
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="高级搜索" name="search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="物料" path="itemId">
              <RemoteSelect
                v-model:value="model.itemId"
                label-field="name"
                value-field="id"
                :api-fn="itemApi.list"
                placeholder="请选择物料"
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="规格" path="skuId">
              <NSelect
                v-model:value="model.skuId"
                :options="skuOptions"
                label-field="name"
                value-field="id"
                placeholder="请选择规格"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi suffix span="24 m:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="search">
                  <template #icon>
                    <icon-ph-magnifying-glass class="text-icon" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="reset">
                  <template #icon>
                    <icon-ph-arrow-counter-clockwise class="text-icon" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
