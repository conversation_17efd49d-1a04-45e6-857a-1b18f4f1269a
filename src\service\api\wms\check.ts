import { request } from '@/service/request';

export const checkApi = {
  // 获取盘点单列表
  list: (params: Api.Wms.CheckSearchParams) =>
    request<Api.Wms.CheckList>({
      url: '/wms/checks',
      params
    }),

  // 创建盘点单
  add: (data: Api.Wms.CheckCreateParams) =>
    request<null>({
      url: '/wms/checks',
      method: 'POST',
      data
    }),

  // 更新盘点单
  save: (data: Api.Wms.CheckUpdateParams) =>
    request<null>({
      url: `/wms/checks/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除盘点单
  del: (id: number) =>
    request<null>({
      url: `/wms/checks/${id}`,
      method: 'DELETE'
    }),

  // 批量删除盘点单
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/checks',
      method: 'DELETE',
      data: { ids }
    })
};
