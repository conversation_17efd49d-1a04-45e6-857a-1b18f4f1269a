<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { metaApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { disableTreeItem } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType | 'addChild';
  /** the edit row data */
  rowData?: Api.Wms.Meta | null;
  /** the meta options */
  metaOptions?: TreeOption[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType | 'addChild', string> = {
    add: '新增分类',
    edit: '编辑分类',
    addChild: '新增子分类'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Wms.Meta, 'name' | 'order' | 'parentId' | 'parentPath' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    order: 0,
    parentId: 0,
    parentPath: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule
};

const metaTree = computed(() => {
  return [{ id: 0, name: '顶级分类', children: props.operateType === 'edit' ? disableTreeItem(props.metaOptions, props.rowData?.id) : props.metaOptions }];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }

  // 新增子栏目
  if (props.operateType === 'addChild') {
    Object.assign(model.value, { parentId: props.rowData?.id });
  }
}

function closeDrawer() {
  visible.value = false;
}

function handleParentChange(value: number, meta: Api.Wms.Meta) {
  model.value.parentId = value;
  model.value.parentPath = meta.parentPath;
}

async function handleSubmit() {
  loading.value = true;

  if (props.operateType === 'addChild') {
    model.value.parentPath = props.rowData?.parentPath.concat([props.rowData?.id]) || [];
  }

  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await metaApi.save(model.value) : await metaApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-500px">
    <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
      <NFormItem label="所属分类" path="parentId">
        <NTreeSelect
          v-model:value="model.parentId"
          :options="metaTree"
          key-field="id"
          label-field="name"
          clearable
          :disabled="operateType === 'addChild'"
          @update:value="handleParentChange"
        />
      </NFormItem>
      <NFormItem label="名称" path="name">
        <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
      </NFormItem>
      <NFormItem label="排序" path="order">
        <NInputNumber v-model:value="model.order" placeholder="请输入排序" :min="0" :precision="0" clearable />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="visible = false">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
