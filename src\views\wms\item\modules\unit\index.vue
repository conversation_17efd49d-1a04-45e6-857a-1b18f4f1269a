<script setup lang="tsx">
import { NButton, NPopconfirm } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import { unitApi } from '@/service/api/wms';
import { useTable, useTableOperate } from '@/hooks/common/table';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateModel from './modules/OperateModel.vue';

const visible = defineModel<boolean>('visible', {
  default: false
});

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  updateSearchParams
} = useTable({
  apiFn: unitApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,asc',
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '名称',
      align: 'left',
      ellipsis: true
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => unitApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await unitApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await unitApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'order,id',
      _order: 'desc,asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <NModal v-model:show="visible" title="物料单位" preset="card" class="w-700px">
    <TableHeaderOperation
      v-model:columns="columnChecks"
      :disabled-delete="checkedRowKeys.length === 0"
      :loading="loading"
      @add="handleAdd"
      @delete="handleBatchDelete"
      @refresh="getData" />
    <NDataTable
      v-model:checked-row-keys="checkedRowKeys"
      :columns="columns"
      :data="data"
      size="small"
      :loading="loading"
      remote
      striped
      :bordered="false"
      :row-key="row => row.id"
      :pagination="pagination"
      class="b-t-1px sm:h-full b-auto m-t-12px min-h-400px"
      @update:sorter="handleSort"
    />
    <OperateModel
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="getDataByPage"
    />
  </NModal>
</template>

<style scoped></style>
