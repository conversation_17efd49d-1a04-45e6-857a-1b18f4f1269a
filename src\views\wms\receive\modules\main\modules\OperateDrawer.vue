<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { receiveApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { suggestCode } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Receive | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增入库单',
    edit: '编辑入库单'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Wms.Receive, 'code' | 'type' | 'summary' | 'status' | 'relatedNo' | 'partnerId'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    code: suggestCode('RK'),
    type: 1,
    summary: '',
    status: 1,
    relatedNo: '',
    partnerId: 0
  };
}

type RuleKey = Extract<keyof Model, 'code' | 'type'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  code: defaultRequiredRule,
  type: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await receiveApi.save(model.value) : await receiveApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="单号" path="code">
          <NInput v-model:value="model.code" placeholder="请输入单号" disabled clearable />
        </NFormItem>
        <NFormItem label="类型" path="type">
          <NSelect
            v-model:value="model.type"
            :options="useDict('number').items('ReceiveType')"
            placeholder="请选择类型"
            clearable
          />
        </NFormItem>
        <NFormItem label="关联单号" path="relatedNo">
          <NInput v-model:value="model.relatedNo" placeholder="请输入关联单号" clearable />
        </NFormItem>
        <NFormItem label="供应商" path="partnerId">
          <RemoteSelect
            v-model:value="model.partnerId"
            label-field="name"
            value-field="id"
            :options="[item]"
            :api-fn="partnerApi.list()"
            disabled
          />
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
